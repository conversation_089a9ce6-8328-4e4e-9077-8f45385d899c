#ifndef _RTW_SCONFIG_H_
#define _RTW_SCONFIG_H_

struct wifi_profile_t {
	char ssid[64];
	char auth[32];
	char sec[32];
	char key[128];  //password
	char key_id;     //wep key index
	char wep;        //0:open,1:wep,2:wpa/wpa2
};

/*****************************************************************************
function name: rtw_sconfig_start
Function:  start simple config
Parameter:  custom_pin_code:8~64Byte pin code
return:	0:SUCCESS, -1:FAIL
*****************************************************************************/
int rtw_sconfig_start(char *custom_pin_code);

/*****************************************************************************
function name: rtw_get_wifi_profile
Function:  get wifi_profile_t info.This function will block waiting for a sema.
Parameter:  profile:return struct wifi_profile_t.
return:	0:SUCCESS, -1:FAIL
*****************************************************************************/
int rtw_get_wifi_profile(struct wifi_profile_t *profile);

/*****************************************************************************
function name: rtw_sconfig_stop
Function:  stop simple config.
Parameter:  NONE
return:	0:SUCCESS, -1:FAIL
*****************************************************************************/
int rtw_sconfig_stop(void);

#endif

