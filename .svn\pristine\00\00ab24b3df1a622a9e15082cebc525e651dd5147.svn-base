/******************************************************************************
Copyright (C) 2021-2023 广州敏视数码科技有限公司版权所有.

文件名：pd.h

日期: 2021-08-03

文件功能描述: 定义行人检测算法功能接口

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

*******************************************************************************/
#ifndef _APC_H_
#define _APC_H_
#include "../../../include/common.h"
#include "config.h"

/* 模块配置参数 / Module configuration parameters */
typedef struct tag_ApcCfg_S
{
    sint32          s32SplitMode;       /* 视频多通道显示方式 */
    uint32          u32ChnNum;          /* PD算法通道数目 */
#if ALG_MUTLIT_BUFFER
    sint32          as32MediaBufFd[4][3]; /* 媒体通道Media Buffer的文件描述符 */
#else
    sint32          as32MediaBufFd[4];  /* 媒体通道Media Buffer的文件描述符 */
#endif
    sint32          as32MediaBufChn[4]; /* 媒体通道Media Buffer的对应通道 */
    CFG_ALG_PARAM   stAlgParam;         /* 算法配置参数 */
} APC_CFG_PARAM_S;

/******************************************************************************
 * 函数功能: 初始化DMS算法模块
 * 输入参数: pstInitParam --- 初始化配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             ERR_NULL_PTR - 传入参数指针为NULL
             SV_FAILURE - 失败
 * 注意    : 无
 *****************************************************************************/
extern sint32 APC_Init(APC_CFG_PARAM_S *pstInitParam);

/******************************************************************************
 * 函数功能: 去初始化DMS算法模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 无
 *****************************************************************************/
extern sint32 APC_Fini();

/******************************************************************************
 * 函数功能: 启动DMS算法模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 APC_Start();

/******************************************************************************
 * 函数功能: 停止DMS算法模块
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 PD_Stop();

/******************************************************************************
 * 函数功能: 设置DMS算法模块配置参数
 * 输入参数: pstConnCfg --- 配置参数
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 APC_ConfigSet(CFG_ALG_PARAM *pstCfgParam);

/******************************************************************************
 * 函数功能: 设置当前总人数
 * 输入参数: s32ToalNum --- 总人数 (-1:无效)
             s32InNum --- 上车人数 (-1:无效)
             s32OutNum --- 下车人数 (-1:无效)
 * 输出参数: 无
 * 返回值  : SV_SUCCESS - 成功
             SV_FAILURE - 其它错误
 * 注意    : 
 *****************************************************************************/
extern sint32 APC_SetApcNum(sint32 s32ToalNum, sint32 s32InNum, sint32 s32OutNum);

#endif  /* _APC_H_ */
