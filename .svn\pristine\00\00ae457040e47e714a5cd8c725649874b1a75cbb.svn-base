/******************************************************************************
Copyright (C) 2017-2019 广州敏视数码科技有限公司版权所有.

文件名：common.c

作者: hjf 版本: v1.0.0(初始版本号)   日期: 2021-11-26

文件功能描述: 定义日志模块功能

其他: // 其他内容说明

版本: v1.0.0(最新版本号)

函数列表: // 主要函数列表,每条记录应包括函数名及功能简要说明

历史记录: // 修改历史记录列表,每条记录应包括修改日期,作者及修改内容简述

*******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <sys/sem.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/prctl.h>
#include <sys/time.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <pthread.h>

#include <sys/ioctl.h>
#include <net/if.h>

#include "common.h"
#include "print.h"
#include "safefunc.h"
#include "cJSON.h"
#include "utils.h"

/******************************************************************************
 * 函数功能: 获取开机时间戳 (ms)
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
sint64 COMMON_GetTimeTickMs()
{
	struct timespec time = {0, 0};

	clock_gettime(CLOCK_MONOTONIC, &time);
	return ((sint64)time.tv_sec) * 1000 + time.tv_nsec/1000000;
}

/******************************************************************************
 * 函数功能: 去除换行符
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
void COMMON_CutLineBreak(char *pszStr)
{
    char *pcTmp = NULL;

    pcTmp = strchr(pszStr, '\r');
	if(NULL == pcTmp)
		pcTmp = strchr(pszStr, '\n');
	if(NULL != pcTmp)
		*pcTmp = '\0';
}

SV_BOOL COMMON_IsAplaying()
{
    char szCmd[128] = {0}, szBuf[512] = {0};
    sprintf(szCmd, "ps -ef | grep aplay | grep -v \"defunct\\|grep\"");
    SAFE_System_Recv(szCmd, szBuf, 512);
    if (NULL != strstr(szBuf, "aplay"))
    {
        return SV_TRUE;
    }
    else
    {
        return SV_FALSE;
    }
}

/******************************************************************************
 * 函数功能: 文件路径是否存在
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
SV_BOOL COMMON_IsPathExist(char *pszPath)
{
    if (SV_SUCCESS == access(pszPath, F_OK))
		return SV_TRUE;
	else
		return SV_FALSE;
}

/******************************************************************************
 * 函数功能: 计算字符串Unicode码长度
 * 输入参数: 无
 * 输出参数: 无
 * 返回值  : 无
 * 注意    : 无
 *****************************************************************************/
uint32 COMMON_UnicodeEncode(char *str)
{
    uint32 i, j;
	uint32 len = strlen(str);
	uint32 u32UniCode[64];
	uint32 count = 0;
    for (i=0,j=0; i<len; ++i)
	{
        if ((str[i] & 0x80) == 0) { // ASCII 码值范围：0 ~ 127
            u32UniCode[j++] = (uint32)str[i];
        } else if ((str[i] & 0xE0) == 0xC0 && i+1<len && (str[i+1] & 0xC0) == 0x80) { // 双字节编码，U+0080 ~ U+07FF
            u32UniCode[j++] = (uint32)((str[i] & 0x1F) << 6) | (str[i+1] & 0x3F);
            ++i;
        } else if ((str[i] & 0xF0) == 0xE0 && i+2<len && (str[i+1] & 0xC0) == 0x80 && (str[i+2] & 0xC0) == 0x80) { //三字节编码，U+08000 ~ U+FFFFF
            u32UniCode[j++] = (uint32)((str[i] & 15)<<12)|((str[++i]&63)<<6)|(str[++i]&63);
        } else {
            printf("Unsupported character: %c\n", str[i]);
            return;
        }
        count+=1;
	}
	return count;
}

sint32 COMMON_CutOutName(const char* pSrc, char* pResut)
{

	char* pTemp = pSrc;
	sint32 s32len = strlen(pSrc);
	sint32 s32Count = 0;

	for(s32Count = 0; s32Count < s32len; s32Count++)
	{
		if( pSrc[s32Count] == '/' )
		{
			pTemp = pSrc + s32Count + 1;
		}
	}

	strcpy(pResut, pTemp);

	return SV_SUCCESS;
}

sint64 COMMON_GetFileSize(const char *pFile)
{
	sint64 filesize = -1;
	struct stat statbuff;
	if(stat(pFile, &statbuff) < 0)
	{
		return filesize;
	}
	else
	{
		filesize = statbuff.st_size;
	}

	return filesize;
}

sint32  COMMON_UTC2Local(struct tm* tm_utc, struct tm* result)
{
	if(!tm_utc || !result)
		return SV_FAILURE;

	time_t	tick;
	tick = timegm(tm_utc);
	localtime_r(&tick, result);

	return SV_SUCCESS;
}

sint32 COMMON_ReadFile(const char* p_file, char* buf, sint32 size)
{
	sint32 fd = -1, s32ReadNum = -1;

	if((fd = open(p_file, O_RDWR | O_CLOEXEC)) < 0)
	{
		return SV_FAILURE;
	}

	if( read(fd, buf, size) <= 0)
	{
		close(fd);
		return SV_FAILURE;
	}

	close(fd);
	return SV_SUCCESS;
}

sint32 COMMON_GetFileDir(const char* szSrc, char* szDir)
{
	char* pTemp = szSrc;
	sint32 s32len = strlen(szSrc);
	sint32 s32Count = 0, s32DirLen = 0;

	for(s32Count = 0; s32Count < s32len; s32Count++)
	{
		if( szSrc[s32Count] == '/' )
		{
			pTemp = szSrc + s32Count;
		}
	}
	s32DirLen = pTemp - szSrc;
	strncpy(szDir, szSrc, s32DirLen);
	szDir[s32DirLen] = 0;

	return SV_SUCCESS;
}

uint32 COMMON_GetTimeTickSecond()
{
	sint32 u32Time = 0;
    struct timespec time1 = {0, 0};
    if(clock_gettime(CLOCK_MONOTONIC, &time1)<0)
        return 0;
    u32Time = time1.tv_sec;
    return u32Time;
}

void COMMON_StartThread(void (*f)(void *), void *p)
{
	pthread_t thread_id = (pthread_t) 0;
	pthread_attr_t attr;
	sint32 s32Ret = 0;

    pthread_attr_init(&attr);
	pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    s32Ret = pthread_create(&thread_id, &attr,(void *(*) (void *) ) f, p);
    if (s32Ret < 0)
    {
        print_level(SV_ERROR, "pthread_create for client failed. [err: %s]\n", strerror(errno));
        pthread_attr_destroy(&attr);
    }
    pthread_attr_destroy(&attr);

    return ;
}

SV_BOOL COMMON_IsNetCardExist(char *pszIfName)
{
    int sock = socket(AF_INET, SOCK_DGRAM, 0);
    if (sock < 0) {
        perror("Socket creation failed");
        return -1;
    }

    struct ifreq ifr;
    memset(&ifr, 0, sizeof(struct ifreq));
    strncpy(ifr.ifr_name, pszIfName, IFNAMSIZ - 1);

    // 通过 ioctl 获取接口索引，如果失败说明接口不存在
    if (ioctl(sock, SIOCGIFINDEX, &ifr) < 0)
    {
        close(sock);
        return SV_FALSE;  // 接口不存在
    }

    close(sock);
    return SV_TRUE; // 接口存在
}

uint32 COMMON_GetAdcValue(uint32 u32AdcChn)
{
    sint32 s32Ret = 0;
    char szCmd[128] = {0};
    char szRecv[128] = {0};
    uint32 u32Voltage = 0;

    sprintf(szCmd, "cat /sys/bus/iio/devices/iio\:device0/in_voltage%d_raw", u32AdcChn);
    s32Ret = SAFE_System_Recv(szCmd, szRecv, 128);
    if (SV_SUCCESS != s32Ret)
    {
        print_level(SV_ERROR, "SAFE_System_Recv cmd: %s failed!\n", szCmd);
    }
    COMMON_CutLineBreak(szRecv);
    u32Voltage = atoi(szRecv);

    return u32Voltage;
}

