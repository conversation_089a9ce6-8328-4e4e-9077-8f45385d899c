root@ZhuoTK:/# insmod qmi_wwan_q.ko 
[  116.910000] qmi_wwan_q 1-1.3:1.4: cdc-wdm0: USB WDM device
[  116.930000] qmi_wwan_q 1-1.3:1.4: Quectel Android work on RawIP mode
[  116.930000] qmi_wwan_q 1-1.3:1.4: rx_urb_size = 1520
[  116.940000] qmi_wwan_q 1-1.3:1.4 wwan0: register 'qmi_wwan_q' at usb-101c0000.ehci-1.3, WWAN/QMI device, 06:fb:51:a3:d6:c5
[  116.950000] usbcore: registered new interface driver qmi_wwan_q

root@ZhuoTK:/# brctl addbr br0
root@ZhuoTK:/# brctl addif br0 eth0.1
root@ZhuoTK:/# brctl addif br0 wwan0
root@ZhuoTK:/# brctl show
bridge name	bridge id		STP enabled	interfaces
br0		8000.00ca019197b9	no		eth0.1
							                wwan0

root@ZhuoTK:/# quectel-CM -s cmnet -b &
root@ZhuoTK:/# [04-13_05:13:39:369] Quectel_QConnectManager_Linux_V1.6.0.25
[04-13_05:13:39:372] Find /sys/bus/usb/devices/1-1.3 idVendor=0x2c7c idProduct=0x125, bus=0x001, dev=0x003
[  143.340000] net wwan0: bridge_mode change to 0x1
[04-13_05:13:39:373] Auto find qmichannel = /dev/cdc-wdm0
[04-13_05:13:39:374] Auto find usbnet_adapter = wwan0
[04-13_05:13:39:374] netcard driver = qmi_wwan_q, driver version = V1.2.0.23
[04-13_05:13:39:380] Modem works in QMI mode
[04-13_05:13:39:388] cdc_wdm_fd = 7
[04-13_05:13:39:466] Get clientWDS = 5
[04-13_05:13:39:496] Get clientDMS = 2
[04-13_05:13:39:527] Get clientNAS = 4
[04-13_05:13:39:559] Get clientUIM = 1
[04-13_05:13:39:592] Get clientWDA = 1
[04-13_05:13:39:626] requestBaseBandVersion EC25EFAR06A11M4G
[04-13_05:13:39:752] requestGetSIMStatus SIMStatus: SIM_READY
[04-13_05:13:39:752] requestSetProfile[1] cmnet///0
[04-13_05:13:39:816] requestGetProfile[1] cmnet///0
[04-13_05:13:39:848] requestRegistrationState2 MCC: 460, MNC: 0, PS: Attached, DataCap: LTE
[04-13_05:13:39:879] requestQueryDataCall IPv4ConnectionStatus: DISCONNECTED
[04-13_05:13:39:880] ifconfig wwan0 0.0.0.0
[04-13_05:13:39:893] ifconfig wwan0 down
[04-13_05:13:39:943] requestSetupDataCall WdsConnectionIPv4Handle: 0x872627c0
[04-13_05:13:40:073] ifconfig wwan0 up
[04-13_05:13:40:085] echo '0xa8d9237' > /sys/class/net/wwan0/bridge_ipv4

root@ZhuoTK:/# ifconfig br0 up
[  165.730000] wwan0 PC Mac Address: 00:0e:c6:a6:6c:f1
[  165.750000] wwan0 PC Mac Address: 00:0e:c6:a6:6c:f1
[  165.860000] wwan0 sip = *************, tip=*************, ipv4=*************
[  165.870000] wwan0 sip = *************, tip=*************, ipv4=*************
[  165.990000] wwan0 sip = *************, tip=*************, ipv4=*************
[  166.010000] wwan0 sip = 0.0.0.0, tip=*************, ipv4=*************
[  166.070000] wwan0 sip = *************, tip=*************, ipv4=*************
[  167.010000] wwan0 sip = 0.0.0.0, tip=*************, ipv4=*************
[  167.480000] br0: port 2(wwan0) entered forwarding state
[  167.520000] br0: port 1(eth0.1) entered forwarding state
[  168.020000] wwan0 sip = 0.0.0.0, tip=*************, ipv4=*************
[  169.010000] wwan0 sip = *************, tip=*************, ipv4=*************
[  169.120000] wwan0 sip = *************, tip=*************, ipv4=*************
[  169.130000] wwan0 sip = *************, tip=*************, ipv4=*************
[  176.620000] wwan0 sip = *************, tip=*************, ipv4=*************
