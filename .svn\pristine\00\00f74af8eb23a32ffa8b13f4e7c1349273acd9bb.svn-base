#if !defined(__IMX224_CMOS_H_)
#define __IMX224_CMOS_H_

#include <stdio.h>
#include <string.h>
#include <assert.h>
#include "hi_comm_sns.h"
#include "hi_comm_video.h"
#include "hi_sns_ctrl.h"
#include "mpi_isp.h"
#include "mpi_ae.h"
#include "mpi_awb.h"
#include "mpi_af.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* End of #ifdef __cplusplus */


/* To change the mode of config. ifndef INIFILE_CONFIG_MODE, quick config mode.*/
/* else, cmos_cfg.ini file config mode*/
#ifdef INIFILE_CONFIG_MODE

extern AE_SENSOR_DEFAULT_S  g_AeDft[];
extern AWB_SENSOR_DEFAULT_S g_AwbDft[];
extern ISP_CMOS_DEFAULT_S   g_IspDft[];
extern HI_S32 Cmos_LoadINIPara(const HI_CHAR *pcName);
#else

#endif

#define myprint(fmt, arg...) do { \
    printf("\033[0;32m %s>%s:#%d, \033[0m "fmt, __FILE__, __FUNCTION__, __LINE__, ## arg);  \
} while(0)

/****************************************************************************
 * local variables                                                            *
 ****************************************************************************/

#define FULL_LINES_MAX  (0xFFFF)

#define SHS1_ADDR (0x220) 
#define GAIN_ADDR (0x214)
#define HCG_ADDR  (0x209)
#define VMAX_ADDR (0x218)
#define HMAX_ADDR (0x21b)
#define SENSOR_1080P_30FPS_MODE (1) 
#define SENSOR_720P_30FPS_MODE  (2) 
#define SENSOR_720P_60FPS_MODE  (3) 
#define INCREASE_LINES (0) /* make real fps less than stand fps because NVR require*/
#define VMAX_720P30     (750+INCREASE_LINES)
#define VMAX_720P60     (750+INCREASE_LINES)

extern HI_U8 gu8SensorImageMode;
extern WDR_MODE_E genSensorMode;

static HI_U32 gu32FullLinesStd = VMAX_720P30; 
static HI_U32 gu32FullLines = VMAX_720P30;
static HI_BOOL bInit = HI_FALSE;
extern HI_BOOL bSensorInit; 

static HI_U8 gu8HCGReg = 0x2;
static ISP_SNS_REGS_INFO_S g_stSnsRegsInfo = {0};
static ISP_SNS_REGS_INFO_S g_stPreSnsRegsInfo = {0};

#if 0
/* Piris attr */
static ISP_PIRIS_ATTR_S gstPirisAttr=
{
    0,      // bStepFNOTableChange
    1,      // bZeroIsMax
    94,     // u16TotalStep
    62,     // u16StepCount
    /* Step-F number mapping table. Must be from small to large. F1.0 is 1024 and F32.0 is 1 */
    {30,35,40,45,50,56,61,67,73,79,85,92,98,105,112,120,127,135,143,150,158,166,174,183,191,200,208,217,225,234,243,252,261,270,279,289,298,307,316,325,335,344,353,362,372,381,390,399,408,417,426,435,444,453,462,470,478,486,493,500,506,512},
    ISP_IRIS_F_NO_1_4, // enMaxIrisFNOTarget
    ISP_IRIS_F_NO_5_6,  // enMinIrisFNOTarget
    0,                 // bFNOExValid
    512,              // u32MaxIrisFNOTarget  
    30                  // u32MinIrisFNOTarget
};
#endif

/* AE default parameter and function */
#ifdef INIFILE_CONFIG_MODE

HI_S32 cmos_get_ae_default_imx224(AE_SENSOR_DEFAULT_S *pstAeSnsDft)
{
    if (HI_NULL == pstAeSnsDft)
    {
        printf("null pointer when get ae default value!\n");
        return -1;
    }
    
    pstAeSnsDft->u32LinesPer500ms = gu32FullLinesStd*30/2;
    pstAeSnsDft->u32FullLinesStd = gu32FullLinesStd;
    pstAeSnsDft->u32FlickerFreq = 0;
    pstAeSnsDft->u32FullLinesMax = FULL_LINES_MAX;

    pstAeSnsDft->stIntTimeAccu.enAccuType = AE_ACCURACY_LINEAR;
    pstAeSnsDft->stIntTimeAccu.f32Accuracy = 1;
    pstAeSnsDft->stIntTimeAccu.f32Offset = 0;

    pstAeSnsDft->stAgainAccu.enAccuType = AE_ACCURACY_TABLE;
    pstAeSnsDft->stAgainAccu.f32Accuracy = 0.1;

    pstAeSnsDft->stDgainAccu.enAccuType = AE_ACCURACY_TABLE;
    pstAeSnsDft->stDgainAccu.f32Accuracy = 0.1;
    
    switch(genSensorMode)
    {
        default:
        case WDR_MODE_NONE:   /*linear mode*/
            pstAeSnsDft->au8HistThresh[0] = 0xd;
            pstAeSnsDft->au8HistThresh[1] = 0x28;
            pstAeSnsDft->au8HistThresh[2] = 0x60;
            pstAeSnsDft->au8HistThresh[3] = 0x80;
    
            pstAeSnsDft->u8AeCompensation = g_AeDft[0].u8AeCompensation;
    
            pstAeSnsDft->u32MaxIntTime = gu32FullLinesStd - 2;
            pstAeSnsDft->u32MinIntTime = 2;
            pstAeSnsDft->u32MaxIntTimeTarget = g_AeDft[0].u32MaxIntTimeTarget;
            pstAeSnsDft->u32MinIntTimeTarget = g_AeDft[0].u32MinIntTimeTarget;
    
            pstAeSnsDft->u32MaxAgain = 16229;  
            pstAeSnsDft->u32MinAgain = 1024;
            pstAeSnsDft->u32MaxAgainTarget = g_AeDft[0].u32MaxAgainTarget;
            pstAeSnsDft->u32MinAgainTarget = g_AeDft[0].u32MinAgainTarget;
    
            pstAeSnsDft->u32MaxDgain = 8134;  
            pstAeSnsDft->u32MinDgain = 1024;
            pstAeSnsDft->u32MaxDgainTarget = g_AeDft[0].u32MaxDgainTarget;
            pstAeSnsDft->u32MinDgainTarget = g_AeDft[0].u32MinDgainTarget;
  
            pstAeSnsDft->u32ISPDgainShift = g_AeDft[0].u32ISPDgainShift;
            pstAeSnsDft->u32MinISPDgainTarget = g_AeDft[0].u32MinISPDgainTarget;
            pstAeSnsDft->u32MaxISPDgainTarget = g_AeDft[0].u32MaxISPDgainTarget;    
        break;
        
    }
    return 0;
}

#else

HI_S32 cmos_get_ae_default_imx224(AE_SENSOR_DEFAULT_S *pstAeSnsDft)
{
    if (HI_NULL == pstAeSnsDft)
    {
        printf("null pointer when get ae default value!\n");
        return -1;
    }
    
    pstAeSnsDft->u32LinesPer500ms = gu32FullLinesStd*30/2;
    pstAeSnsDft->u32FullLinesStd = gu32FullLinesStd;
    pstAeSnsDft->u32FlickerFreq = 0;
    pstAeSnsDft->u32FullLinesMax = FULL_LINES_MAX;

    pstAeSnsDft->stIntTimeAccu.enAccuType = AE_ACCURACY_LINEAR;
    pstAeSnsDft->stIntTimeAccu.f32Accuracy = 1;
    pstAeSnsDft->stIntTimeAccu.f32Offset = 0;

    pstAeSnsDft->stAgainAccu.enAccuType = AE_ACCURACY_TABLE;
    pstAeSnsDft->stAgainAccu.f32Accuracy = 0.1;

    pstAeSnsDft->stDgainAccu.enAccuType = AE_ACCURACY_TABLE;
    pstAeSnsDft->stDgainAccu.f32Accuracy = 0.1;
    
    pstAeSnsDft->u32ISPDgainShift = 8;
    //pstAeSnsDft->u32MinISPDgainTarget = 2 << pstAeSnsDft->u32ISPDgainShift;
    pstAeSnsDft->u32MinISPDgainTarget = 567;
    pstAeSnsDft->u32MaxISPDgainTarget = 16 << pstAeSnsDft->u32ISPDgainShift; 

    //memcpy(&pstAeSnsDft->stPirisAttr, &gstPirisAttr, sizeof(ISP_PIRIS_ATTR_S));
    pstAeSnsDft->enMaxIrisFNO = ISP_IRIS_F_NO_1_4;
    pstAeSnsDft->enMinIrisFNO = ISP_IRIS_F_NO_5_6;

    pstAeSnsDft->bAERouteExValid = HI_FALSE;
    pstAeSnsDft->stAERouteAttr.u32TotalNum = 0;
    pstAeSnsDft->stAERouteAttrEx.u32TotalNum = 0;
    
    switch(genSensorMode)
    {
        default:
        case WDR_MODE_NONE:   /*linear mode*/
            pstAeSnsDft->au8HistThresh[0] = 0xd;
            pstAeSnsDft->au8HistThresh[1] = 0x28;
            pstAeSnsDft->au8HistThresh[2] = 0x60;
            pstAeSnsDft->au8HistThresh[3] = 0x80;
    
            pstAeSnsDft->u8AeCompensation = 0x38; 
    
            pstAeSnsDft->u32MaxIntTime = gu32FullLinesStd - 2;
            pstAeSnsDft->u32MinIntTime = 2;
            pstAeSnsDft->u32MaxIntTimeTarget = 65535;
            pstAeSnsDft->u32MinIntTimeTarget = 2;
            pstAeSnsDft->u32MaxAgain = 16229;
            pstAeSnsDft->u32MinAgain = 2998;
            pstAeSnsDft->u32MaxAgainTarget = pstAeSnsDft->u32MaxAgain;
            pstAeSnsDft->u32MinAgainTarget = pstAeSnsDft->u32MinAgain;
    
            pstAeSnsDft->u32MaxDgain = 8134;  
            pstAeSnsDft->u32MinDgain = 1024;
            pstAeSnsDft->u32MaxDgainTarget = pstAeSnsDft->u32MaxDgain;
            pstAeSnsDft->u32MinDgainTarget = pstAeSnsDft->u32MinDgain;
        break;
        
    }
    return 0;
}

#endif

/* the function of sensor set fps */
HI_VOID cmos_fps_set_imx224(HI_FLOAT f32Fps, AE_SENSOR_DEFAULT_S *pstAeSnsDft)
{
    if (SENSOR_720P_30FPS_MODE == gu8SensorImageMode)
    {
        if ((f32Fps <= 30) && (f32Fps >= 0.51))
        {
            gu32FullLinesStd = VMAX_720P30*30/f32Fps;
        }
        else
        {
            printf("Not support Fps: %f\n", f32Fps);
            return;
        }
    }
    else if (SENSOR_720P_60FPS_MODE == gu8SensorImageMode)
    {
        if ((f32Fps <= 60) && (f32Fps >= 0.68))
        {
            gu32FullLinesStd = VMAX_720P60*60/f32Fps;
        }
        else
        {
            printf("Not support Fps: %f\n", f32Fps);
            return;
        }
        
    }
    else
    {
        printf("Not support! gu8SensorImageMode:%d, f32Fps:%f\n", gu8SensorImageMode, f32Fps);
        return;
    }

    gu32FullLinesStd = (gu32FullLinesStd > FULL_LINES_MAX) ? FULL_LINES_MAX : gu32FullLinesStd;

    g_stSnsRegsInfo.astSspData[6].u32Data = (gu32FullLinesStd & 0xFF);/* VMAX[7:0] */
    g_stSnsRegsInfo.astSspData[7].u32Data = ((gu32FullLinesStd & 0xFF00) >> 8);/* VMAX[15:8] */
    g_stSnsRegsInfo.astSspData[8].u32Data = ((gu32FullLinesStd & 0x10000) >> 16);/* VMAX[16] */

    pstAeSnsDft->f32Fps = f32Fps;
    pstAeSnsDft->u32LinesPer500ms = gu32FullLinesStd * f32Fps / 2;
    pstAeSnsDft->u32MaxIntTime = gu32FullLinesStd - 2;
    pstAeSnsDft->u32FullLinesStd = gu32FullLinesStd;
    pstAeSnsDft->u32FullLines = gu32FullLinesStd;
    

    gu32FullLines = gu32FullLinesStd;

    return;
}

HI_VOID cmos_slow_framerate_set_imx224(HI_U32 u32FullLines, AE_SENSOR_DEFAULT_S *pstAeSnsDft)
{
    u32FullLines = (u32FullLines > FULL_LINES_MAX) ? FULL_LINES_MAX : u32FullLines;
    gu32FullLines = u32FullLines;
    pstAeSnsDft->u32FullLines = gu32FullLines;

    g_stSnsRegsInfo.astSspData[6].u32Data = (gu32FullLines & 0xFF);
    g_stSnsRegsInfo.astSspData[7].u32Data = ((gu32FullLines & 0xFF00) >> 8);
    g_stSnsRegsInfo.astSspData[8].u32Data = ((gu32FullLinesStd & 0x10000) >> 16);

    pstAeSnsDft->u32MaxIntTime = gu32FullLines - 2;
    
    return;
}

/* while isp notify ae to update sensor regs, ae call these funcs. */
HI_VOID cmos_inttime_update_imx224(HI_U32 u32IntTime)
{
    HI_U32 u32Value = gu32FullLines - u32IntTime;

    g_stSnsRegsInfo.astSspData[0].u32Data = (u32Value & 0xFF);
    g_stSnsRegsInfo.astSspData[1].u32Data = ((u32Value & 0xFF00) >> 8);   
    g_stSnsRegsInfo.astSspData[2].u32Data = ((u32Value & 0x10000) >> 16);   

    return;
}

static HI_U32 digital_gain_table[61]=
{
    1024,  1060,  1097,  1136,  1176,  1217,  1260,  1304,  1350,  1397,  1446,   1497,  1550,   1604,  1661,   1719,  
    1780,  1842,  1907,  1974,  2043,  2115,  2189,  2266,  2346,  2428,  2514,   2602,  2693,   2788,  2886,   2987,  
    3092,  3201,  3314,  3430,  3551,  3675,  3805,  3938,  4077,  4220,  4368,   4522,  4681,   4845,  5015,   5192,  
    5374,  5563,  5758,  5961,  6170,  6387,  6611,  6844,  7084,  7333,  7591,   7858,  8134  
};


static HI_U32 analog_gain_table[81] =
{
     1024 , 1060 ,  1097 ,  1136 ,  1176,  1217 , 1260 ,  1304,  1350 ,  1397 ,  1446 ,  1497 , 1550 , 1604 ,  1661 ,  1719 , 
     1780 , 1842 ,  1907 ,  1974 ,  2043,  2115 , 2189 ,  2266,  2346 ,  2428 ,  2514 ,  2602 , 2693 , 2788 ,  2886 ,  2987 , 
     3092 , 3201 ,  3314 ,  3430 ,  3551,  3675 , 3805 ,  3938,  4077 ,  4220 ,  4368 ,  4522 , 4681 , 4845 ,  5015 ,  5192 , 
     5374 , 5563 ,  5758 ,  5961 ,  6170,  6387 , 6611 ,  6844,  7084 ,  7333 ,  7591 ,  7858 , 8134 , 8420 ,  8716 ,  9022 , 
     9339 , 9667 , 10007 , 10359 , 10723, 11099 ,11489 , 11893, 12311 , 12744 , 13192 , 13655 ,14135 ,14632 , 15146 , 15678 , 
    16229     

};


HI_VOID cmos_again_calc_table_imx224(HI_U32 *pu32AgainLin, HI_U32 *pu32AgainDb)
{
    int i;

    if (*pu32AgainLin >= analog_gain_table[80])
    {
         *pu32AgainLin = analog_gain_table[80];
         *pu32AgainDb = 80;
         return ;
    }
    
    for (i = 1; i < 81; i++)
    {
        if (*pu32AgainLin < analog_gain_table[i])
        {
            *pu32AgainLin = analog_gain_table[i - 1];
            *pu32AgainDb = i - 1;
            break;
        }
    }
    
    return;
}

HI_VOID cmos_dgain_calc_table_imx224(HI_U32 *pu32DgainLin, HI_U32 *pu32DgainDb)
{
    int i;

    if (*pu32DgainLin >= digital_gain_table[60])
    {
         *pu32DgainLin = digital_gain_table[60];
         *pu32DgainDb = 60;
         return ;
    }
    
    for (i = 1; i < 61; i++)
    {
        if (*pu32DgainLin < digital_gain_table[i])
        {
            *pu32DgainLin = digital_gain_table[i - 1];
            *pu32DgainDb = i - 1;
            break;
        }
    }

    return;
}

HI_VOID cmos_gains_update_imx224(HI_U32 u32Again, HI_U32 u32Dgain)
{  
    HI_U32 u32HCG = gu8HCGReg;
    HI_U32 u32Tmp;

    //myprint("u32Again:%d, u32Dgain:%d\n", u32Again, u32Dgain);
    if(u32Again >= 31)
    {
        u32HCG = u32HCG | 0x10;  // bit[4] HCG  .Reg0x3009[7:0]
        u32Again = u32Again - 31;
    }

    u32Tmp=u32Again+u32Dgain;
    u32Tmp*=10;
    g_stSnsRegsInfo.astSspData[3].u32Data = (u32Tmp & 0xFF);
    g_stSnsRegsInfo.astSspData[4].u32Data = ((u32Tmp & 0x300) >> 8); 
    if (WDR_MODE_NONE == genSensorMode)
    {
        g_stSnsRegsInfo.astSspData[5].u32Data = (u32HCG & 0xFF);
    }

    return;
}


/* AWB default parameter and function */
#ifdef INIFILE_CONFIG_MODE

HI_S32 cmos_get_awb_default_imx224(AWB_SENSOR_DEFAULT_S *pstAwbSnsDft)
{
    HI_U8 i;
    
    if (HI_NULL == pstAwbSnsDft)
    {
        printf("null pointer when get awb default value!\n");
        return -1;
    }

    memset(pstAwbSnsDft, 0, sizeof(AWB_SENSOR_DEFAULT_S));
    switch (genSensorMode)
    {
        default:
        case WDR_MODE_NONE:            
            pstAwbSnsDft->u16WbRefTemp = g_AwbDft[0].u16WbRefTemp;

            for(i= 0; i < 4; i++)
            {
                pstAwbSnsDft->au16GainOffset[i] = g_AwbDft[0].au16GainOffset[i];
            }
   
            for(i= 0; i < 6; i++)
            {
                pstAwbSnsDft->as32WbPara[i] = g_AwbDft[0].as32WbPara[i];
            }
            memcpy(&pstAwbSnsDft->stCcm, &g_AwbDft[0].stCcm, sizeof(AWB_CCM_S));
            memcpy(&pstAwbSnsDft->stAgcTbl, &g_AwbDft[0].stAgcTbl, sizeof(AWB_AGC_TABLE_S));
        break;
        
    }
    return 0;
}

#else

static AWB_CCM_S g_stAwbCcm =
{

    5120,
    {	
	    0x01fc, 0x80dc, 0x8020,
	    0x8055, 0x01aa, 0x8055,
	    0x0019, 0x80db, 0x01c2
    },
    3633,
    {
        0x01ef, 0x80bb, 0x8034,
        0x807f, 0x01c7, 0x8048,
        0x001b, 0x80fc, 0x01e1
    },
    2465,
    {
        0x01ca, 0x8097, 0x8033,
        0x8099, 0x01c7, 0x802e,
        0x0007, 0x80f7, 0x01f0
    }

/*
    5120,
    {   
        0x01e0, 0x80c8, 0x8018,
        0x804b, 0x018f, 0x8044,
        0x0019, 0x80d5, 0x01bc
    },
    3633,
    {
        0x01cf, 0x80a8, 0x8027,
        0x8072, 0x01a8, 0x8036,
        0x001a, 0x80ed, 0x01d3
    },
    2465,
    {
        0x01ce, 0x80ad, 0x8021,
        0x807c, 0x0192, 0x8016,
        0x0022, 0x80fc, 0x01da
    }
 */
};

static AWB_AGC_TABLE_S g_stAwbAgcTable =
{
    /* bvalid */
    1,

    /* saturation */
    {0x82,0x82,0x80,0x7c,0x70,0x69,0x5c,0x5c,0x57,0x57,0x50,0x50,0x58,0x48,0x40,0x38}
};

HI_S32 cmos_get_awb_default_imx224(AWB_SENSOR_DEFAULT_S *pstAwbSnsDft)
{
    if (HI_NULL == pstAwbSnsDft)
    {
        printf("null pointer when get awb default value!\n");
        return -1;
    }

    memset(pstAwbSnsDft, 0, sizeof(AWB_SENSOR_DEFAULT_S));

    pstAwbSnsDft->u16WbRefTemp = 5000;

 /*   pstAwbSnsDft->au16GainOffset[0] = 0x1c0;
    pstAwbSnsDft->au16GainOffset[1] = 0x100;
    pstAwbSnsDft->au16GainOffset[2] = 0x100;
    pstAwbSnsDft->au16GainOffset[3] = 0x1e4;

    pstAwbSnsDft->as32WbPara[0] = -40;
    pstAwbSnsDft->as32WbPara[1] = 296;
    pstAwbSnsDft->as32WbPara[2] = 0;
    pstAwbSnsDft->as32WbPara[3] = 169788;
    pstAwbSnsDft->as32WbPara[4] = 128;
    pstAwbSnsDft->as32WbPara[5] = -119571;
*/

    pstAwbSnsDft->au16GainOffset[0] = 0x1dc;
    pstAwbSnsDft->au16GainOffset[1] = 0x100;
    pstAwbSnsDft->au16GainOffset[2] = 0x100;
    pstAwbSnsDft->au16GainOffset[3] = 0x1c9;

    pstAwbSnsDft->as32WbPara[0] = 34;
    pstAwbSnsDft->as32WbPara[1] = 120;
    pstAwbSnsDft->as32WbPara[2] = -102;
    pstAwbSnsDft->as32WbPara[3] = 186418;
    pstAwbSnsDft->as32WbPara[4] = 128;
    pstAwbSnsDft->as32WbPara[5] = -137285;


    memcpy(&pstAwbSnsDft->stCcm, &g_stAwbCcm, sizeof(AWB_CCM_S));
    memcpy(&pstAwbSnsDft->stAgcTbl, &g_stAwbAgcTable, sizeof(AWB_AGC_TABLE_S));

    return 0;
}

#endif


/* ISP default parameter and function */
#ifdef INIFILE_CONFIG_MODE

HI_U32 cmos_get_isp_default_imx224(ISP_CMOS_DEFAULT_S *pstDef)
{   
    if (HI_NULL == pstDef)
    {
        printf("null pointer when get isp default value!\n");
        return -1;
    }

    memset(pstDef, 0, sizeof(ISP_CMOS_DEFAULT_S));
          
    switch (genSensorMode)
    {
        default:
        case WDR_MODE_NONE:           
            memcpy(&pstDef->stDrc, &g_IspDft[0].stDrc, sizeof(ISP_CMOS_DRC_S));
            memcpy(&pstDef->stNoiseTbl, &g_IspDft[0].stNoiseTbl, sizeof(ISP_CMOS_NOISE_TABLE_S));            
            memcpy(&pstDef->stDemosaic, &g_IspDft[0].stDemosaic, sizeof(ISP_CMOS_DEMOSAIC_S));
            memcpy(&pstDef->stRgbSharpen, &g_IspDft[0].stRgbSharpen, sizeof(ISP_CMOS_RGBSHARPEN_S));
            memcpy(&pstDef->stGamma, &g_IspDft[0].stGamma, sizeof(ISP_CMOS_GAMMA_S));
            memcpy(&pstDef->stGe, &g_IspDft[0].stGe, sizeof(ISP_CMOS_GE_S));
        break;           

        break;

    }
    pstDef->stSensorMaxResolution.u32MaxWidth  = 2592;
    pstDef->stSensorMaxResolution.u32MaxHeight = 1944;

    return 0;
}

#else


#if 1
#define DMNR_CALIB_CARVE_NUM_imx224 12

float g_coef_calib_imx224[DMNR_CALIB_CARVE_NUM_imx224][4] = 
{
    {100.000000f, 2.000000f, 0.037048f, 9.002845f, }, 
    {204.000000f, 2.309630f, 0.038180f, 9.011998f, }, 
    {407.000000f, 2.609594f, 0.039593f, 9.160780f, }, 
    {812.000000f, 2.909556f, 0.042155f, 9.453709f, }, 
    {1640.000000f, 3.214844f, 0.047406f, 9.905107f, }, 
    {3223.000000f, 3.508260f, 0.055695f, 11.039407f, }, 
    {6457.000000f, 3.810031f, 0.068628f, 13.358517f, }, 
    {10311.000000f, 4.013301f, 0.080570f, 16.527088f, }, 
    {29899.000000f, 4.475657f, 0.112936f, 25.649172f, },
    {35111.000000f, 4.545443f, 0.092135f, 36.210743f, }, 
    {50897.000000f, 4.706692f, 0.100546f, 43.523537f, },
    {74403.000000f, 4.871591f, 0.095404f, 60.593433f, },
};
#else
#define DMNR_CALIB_CARVE_NUM_imx224 9

float g_coef_calib_imx224[DMNR_CALIB_CARVE_NUM_imx224][4] = 
{
    {100.000000f, 2.000000f, 0.000000f, 21.000000f, }, 
    {200.000000f, 2.301030f, 0.000000f, 21.000000f, }, 
    {400.000000f, 2.602060f, 0.000000f, 38.000000f, }, 
    {800.000000f, 2.903090f, 0.000000f, 46.000000f, }, 
    {1600.000000f, 3.204120f, 0.000000f, 86.000000f, }, 
    {2800.000000f, 3.447158f, 0.000000f, 94.000000f, }, 
    {4300.000000f, 3.633468f, 0.000000f, 92.000000f, }, 
    {17000.000000f, 4.230449f, 0.000000f, 184.000000f, }, 
    {23000.000000f, 4.361728f, 0.000000f, 259.000000f, }, 
};
#endif

static ISP_NR_ISO_PARA_TABLE_S g_stNrIsoParaTab[HI_ISP_NR_ISO_LEVEL_MAX] = 
{
     //u16Threshold//u8varStrength//u8fixStrength//u8LowFreqSlope	
       {1500,       256-96,             256-256,            0 },  //100    //                      //           
       {1500,       256-96,             256-256,            0 },  //200    // ISO                  // ISO //u8LowFreqSlope
       {1500,       256-96,             256-216,            0 },  //400    //{400,  1200, 96,256}, //{400 , 0  }
       {1750,       256-96,             256-196,            2 },  //800    //{800,  1400, 80,256}, //{600 , 2  }
       {2800,       256-72,             256-186,            8 },  //1600   //{1600, 1200, 72,256}, //{800 , 8  }
       {3000,       256-66,             256-196,           12 },  //3200   //{3200, 1200, 64,256}, //{1000, 12 }
       {3500,       256-76,             256-196,            6 },  //6400   //{6400, 1100, 56,256}, //{1600, 6  }
       {1375,       256-76,             256-176,            0 },  //12800  //{12000,1100, 48,256}, //{2400, 0  }
       {1375,       256-66,             256-196,            0 },  //25600  //{36000,1100, 48,256}, //
       {1375,      256-128,             256-196,            0 },  //51200  //{64000,1100, 96,256}, //
       {1250,      256-192,             256-206,            0 },  //102400 //{82000,1000,240,256}, //
       {1250,      256-224,             256-226,            0 },  //204800 //                           //
       {1250,      256-224,             256-256,            0 },  //409600 //                           //
       {1250,      256-224,             256-256,            0 },  //819200 //                           //
       {1250,      256-240,             256-256,            0 },  //1638400//                           //
       {1250,      256-240,             256-256,            0 },  //3276800//                           //
};


static ISP_CMOS_DEMOSAIC_S g_stIspDemosaic =
{
	/*For Demosaic*/
	1, /*bEnable*/			
	16,/*u16VhLimit*/	
	8,/*u16VhOffset*/
	24,   /*u16VhSlope*/
	/*False Color*/
	1,    /*bFcrEnable*/
	{ 8, 8, 8, 8, 8, 8, 8, 8, 3, 0, 0, 0, 0, 0, 0, 0},    /*au8FcrStrength[ISP_AUTO_ISO_STENGTH_NUM]*/
	{24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24},    /*au8FcrThreshold[ISP_AUTO_ISO_STENGTH_NUM]*/
	/*For Ahd*/
	400, /*u16UuSlope*/	
	{512,512,512,512,512,512,512,  400,  0,0,0,0,0,0,0,0}    /*au16NpOffset[ISP_AUTO_ISO_STENGTH_NUM]*/	
};

static ISP_CMOS_GE_S g_stIspGe =
{
	/*For GE*/
	1,    /*bEnable*/			
	9,    /*u8Slope*/	
	9,    /*u8SensiSlope*/	
	300, /*u16SensiThr*/	
	300, /*u16SensiThreshold*/
	{300,300,300,300,310,310,310,  310,  320,320,320,320,330,330,330,330} /*au16Threshold[ISP_AUTO_ISO_STRENGTH_NUM]*/
};
static ISP_CMOS_RGBSHARPEN_S g_stIspRgbSharpen =
{      
  //{100,200,400,800,1600,3200,6400,12800,25600,51200,102400,204800,409600,819200,1638400,3276800};
    {0,	  0,   0,  0,   0,   0,   0,    0,    0,    1,    1,     1,     1,     1,     1,       1},/* enPixSel */
    {10, 15,  30, 30,  35,  40,  50,   50,   60,   80,   90,    90,   100,   100,   110,     110},/*SharpenUD*/
    {25, 30,  30, 30,  35,  40,  50,   55,   50,   60,   70,    80,   100,   110,   120,     130},/*SharpenD*/
    {0,   2,   4,  6,   6,  12,  30,   60,   40,    5,    5,     0,     0,     0,     0,       0},/*NoiseThd*/
    {2,   4,   8, 16,  25,  20,  18,   10,   12,   10,    5,     0,     0,     0,     0,       0},/*EdgeThd2*/
    {230,210, 200,190, 170, 150, 130, 100,  100,   80,   70,    70,    60,    50,    20,      20},/*overshootAmt*/
    {220,220, 200,180, 170, 140, 150, 130,  110,   95,   75,    60,    50,    50,    40,      40},/*undershootAmt*/
};

static ISP_CMOS_UVNR_S g_stIspUVNR = 
{
   //{100,	200, 400, 800, 1600, 3200, 6400, 12800,	25600, 51200, 102400, 204800, 409600, 819200, 1638400, 3276800};
	  {1,	  2,   4,   5,    7,   10,   12,    16,    18,    20,     22,     24,     24,     24,      24,      24},  /*UVNRThreshold*/
 	  {0,	  0,   0,   0,	  0, 	0,    0,     0,     0,	   1,      1,      2,      2,      2,       2,       2},  /*Coring_lutLimit*/
      {0,	  0,   0,  16,   34,   34,   34,    34,    34,    34,     34,     34,     34,     34,      34,      34}   /*UVNR_blendRatio*/
};

static ISP_CMOS_DPC_S g_stCmosDpc = 
{
	//0,/*IR_channel*/
	//0,/*IR_position*/
	{45,100,152,152,220,224,224,224,224,224,224,224,224,224,224,224},/*au16Strength[16]*/
	{0,0,0,0,0,0,0,0x30,0x60,0x80,0x80,0x80,0xE5,0xE5,0xE5,0xE5},/*au16BlendRatio[16]*/
};

static ISP_CMOS_DRC_S g_stIspDrc =
{
    0,
    10,
    0,
    2,
    192,
    60,
    0,
    0,
    0,
    {1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024,1024}
};
static ISP_CMOS_GAMMA_S g_stIspGamma =
{
    /* bvalid */
    1,
    
#if 0    
    {0, 180, 320, 426, 516, 590, 660, 730, 786, 844, 896, 946, 994, 1040, 1090, 1130, 1170, 1210, 1248,
    1296, 1336, 1372, 1416, 1452, 1486, 1516, 1546, 1580, 1616, 1652, 1678, 1714, 1742, 1776, 1798, 1830,
    1862, 1886, 1912, 1940, 1968, 1992, 2010, 2038, 2062, 2090, 2114, 2134, 2158, 2178, 2202, 2222, 2246,
    2266, 2282, 2300, 2324, 2344, 2360, 2372, 2390, 2406, 2422, 2438, 2458, 2478, 2494, 2510, 2526, 2546,
    2562, 2582, 2598, 2614, 2630, 2648, 2660, 2670, 2682, 2698, 2710, 2724, 2736, 2752, 2764, 2780, 2792,
    2808, 2820, 2836, 2848, 2864, 2876, 2888, 2896, 2908, 2920, 2928, 2940, 2948, 2960, 2972, 2984, 2992,
    3004, 3014, 3028, 3036, 3048, 3056, 3068, 3080, 3088, 3100, 3110, 3120, 3128, 3140, 3148, 3160, 3168,
    3174, 3182, 3190, 3202, 3210, 3218, 3228, 3240, 3256, 3266, 3276, 3288, 3300, 3306, 3318, 3326, 3334,
    3342, 3350, 3360, 3370, 3378, 3386, 3394, 3398, 3406, 3414, 3422, 3426, 3436, 3444, 3454, 3466, 3476,
    3486, 3498, 3502, 3510, 3518, 3526, 3530, 3538, 3546, 3554, 3558, 3564, 3570, 3574, 3582, 3590, 3598,
    3604, 3610, 3618, 3628, 3634, 3640, 3644, 3652, 3656, 3664, 3670, 3678, 3688, 3696, 3700, 3708, 3712,
    3716, 3722, 3730, 3736, 3740, 3748, 3752, 3756, 3760, 3766, 3774, 3778, 3786, 3790, 3800, 3808, 3812,
    3816, 3824, 3830, 3832, 3842, 3846, 3850, 3854, 3858, 3862, 3864, 3870, 3874, 3878, 3882, 3888, 3894,
    3900, 3908, 3912, 3918, 3924, 3928, 3934, 3940, 3946, 3952, 3958, 3966, 3974, 3978, 3982, 3986, 3990,
    3994, 4002, 4006, 4010, 4018, 4022, 4032, 4038, 4046, 4050, 4056, 4062, 4072, 4076, 4084, 4090, 4095
    }
#else  /*higher  contrast*/
    {0   ,120 ,220 ,310 ,390 ,470 ,540 ,610 ,670 ,730 ,786 ,842 ,894 ,944 ,994 ,1050,    
    1096,1138,1178,1218,1254,1280,1314,1346,1378,1408,1438,1467,1493,1519,1543,1568,    
    1592,1615,1638,1661,1683,1705,1726,1748,1769,1789,1810,1830,1849,1869,1888,1907,    
    1926,1945,1963,1981,1999,2017,2034,2052,2069,2086,2102,2119,2136,2152,2168,2184,    
    2200,2216,2231,2247,2262,2277,2292,2307,2322,2337,2351,2366,2380,2394,2408,2422,    
    2436,2450,2464,2477,2491,2504,2518,2531,2544,2557,2570,2583,2596,2609,2621,2634,    
    2646,2659,2671,2683,2696,2708,2720,2732,2744,2756,2767,2779,2791,2802,2814,2825,    
    2837,2848,2859,2871,2882,2893,2904,2915,2926,2937,2948,2959,2969,2980,2991,3001,    
    3012,3023,3033,3043,3054,3064,3074,3085,3095,3105,3115,3125,3135,3145,3155,3165,    
    3175,3185,3194,3204,3214,3224,3233,3243,3252,3262,3271,3281,3290,3300,3309,3318,    
    3327,3337,3346,3355,3364,3373,3382,3391,3400,3409,3418,3427,3436,3445,3454,3463,    
    3471,3480,3489,3498,3506,3515,3523,3532,3540,3549,3557,3566,3574,3583,3591,3600,    
    3608,3616,3624,3633,3641,3649,3657,3665,3674,3682,3690,3698,3706,3714,3722,3730,    
    3738,3746,3754,3762,3769,3777,3785,3793,3801,3808,3816,3824,3832,3839,3847,3855,    
    3862,3870,3877,3885,3892,3900,3907,3915,3922,3930,3937,3945,3952,3959,3967,3974,    
    3981,3989,3996,4003,4010,4018,4025,4032,4039,4046,4054,4061,4068,4075,4082,4089,4095}
#endif
};

HI_U32 cmos_get_isp_default_imx224(ISP_CMOS_DEFAULT_S *pstDef)
{   
    if (HI_NULL == pstDef)
    {
        printf("null pointer when get isp default value!\n");
        return -1;
    }

    memset(pstDef, 0, sizeof(ISP_CMOS_DEFAULT_S));
    
    memcpy(&pstDef->stDrc, &g_stIspDrc, sizeof(ISP_CMOS_DRC_S));
    memcpy(&pstDef->stDemosaic, &g_stIspDemosaic, sizeof(ISP_CMOS_DEMOSAIC_S));
	memcpy(&pstDef->stGamma, &g_stIspGamma, sizeof(ISP_CMOS_GAMMA_S));		
    memcpy(&pstDef->stGe, &g_stIspGe, sizeof(ISP_CMOS_GE_S));	

    pstDef->stNoiseTbl.stNrCaliPara.u8CalicoefRow = DMNR_CALIB_CARVE_NUM_imx224;
    pstDef->stNoiseTbl.stNrCaliPara.pCalibcoef    = (HI_FLOAT (*)[4])g_coef_calib_imx224;
	memcpy(&pstDef->stNoiseTbl.stIsoParaTable[0], &g_stNrIsoParaTab[0],sizeof(ISP_NR_ISO_PARA_TABLE_S)*HI_ISP_NR_ISO_LEVEL_MAX);
	
    memcpy(&pstDef->stRgbSharpen, &g_stIspRgbSharpen, sizeof(ISP_CMOS_RGBSHARPEN_S));
	memcpy(&pstDef->stUvnr,       &g_stIspUVNR,       sizeof(ISP_CMOS_UVNR_S));
	memcpy(&pstDef->stDpc,       &g_stCmosDpc,       sizeof(ISP_CMOS_DPC_S));

    pstDef->stSensorMaxResolution.u32MaxWidth  = 1920;
    pstDef->stSensorMaxResolution.u32MaxHeight = 1080;

    return 0;
}

#endif

HI_U32 cmos_get_isp_black_level_imx224(ISP_CMOS_BLACK_LEVEL_S *pstBlackLevel)
{
    if (HI_NULL == pstBlackLevel)
    {
        printf("null pointer when get isp black level value!\n");
        return -1;
    }

    /* Don't need to update black level when iso change */
    pstBlackLevel->bUpdate = HI_FALSE;
            
    pstBlackLevel->au16BlackLevel[0] = 0x10;
    pstBlackLevel->au16BlackLevel[1] = 0x10;
    pstBlackLevel->au16BlackLevel[2] = 0x10;
    pstBlackLevel->au16BlackLevel[3] = 0x10;

    return 0;    
}

HI_VOID cmos_set_pixel_detect_imx224(HI_BOOL bEnable)
{
    HI_U32 u32FullLines_5Fps = VMAX_720P30;
    HI_U32 u32MaxExpTime_5Fps = VMAX_720P30 - 2;
    
    if (SENSOR_720P_30FPS_MODE == gu8SensorImageMode)
    {
        u32FullLines_5Fps = VMAX_720P30 * 30 / 5;
    }
    else if (SENSOR_720P_60FPS_MODE == gu8SensorImageMode)
    {
        u32FullLines_5Fps = VMAX_720P60 * 60 / 5;
    }
    else
    {
        return;
    }

    u32FullLines_5Fps = (u32FullLines_5Fps > FULL_LINES_MAX) ? FULL_LINES_MAX : u32FullLines_5Fps;
    u32MaxExpTime_5Fps = 2;
    
    if (bEnable) /* setup for ISP pixel calibration mode */
    {
        sensor_write_register(VMAX_ADDR, u32FullLines_5Fps & 0xFF); 
        sensor_write_register(VMAX_ADDR + 1, (u32FullLines_5Fps & 0xFF00) >> 8);
        sensor_write_register(SHS1_ADDR, u32MaxExpTime_5Fps & 0xFF);    /* shutter */
        sensor_write_register(SHS1_ADDR +1, (u32MaxExpTime_5Fps & 0xFF00) >> 8);
        sensor_write_register(GAIN_ADDR, 0x00); //gain
    }
    else /* setup for ISP 'normal mode' */
    {
        gu32FullLinesStd = (gu32FullLinesStd > FULL_LINES_MAX) ? FULL_LINES_MAX : gu32FullLinesStd;
        sensor_write_register (VMAX_ADDR, gu32FullLinesStd & 0xFF); 
        sensor_write_register (VMAX_ADDR + 1, (gu32FullLinesStd & 0xFF00) >> 8);

        bInit = HI_FALSE;
    }

    return;
}

HI_VOID cmos_set_wdr_mode_imx224(HI_U8 u8Mode)
{
    bInit = HI_FALSE;
    
    switch(u8Mode)
    {
        case WDR_MODE_NONE:
            genSensorMode = WDR_MODE_NONE;
            printf("linear mode\n");
        break;

        default:
            printf("NOT support this mode!\n");
            return;
        break;
    }
    
    return;
}

HI_S32 cmos_set_image_mode_imx224(ISP_CMOS_SENSOR_IMAGE_MODE_S *pstSensorImageMode)
{
    HI_U8 u8SensorImageMode = gu8SensorImageMode;
    
    bInit = HI_FALSE;    
        
    if (HI_NULL == pstSensorImageMode )
    {
        printf("null pointer when set image mode\n");
        return -1;
    }

    if((pstSensorImageMode->u16Width <= 1280)&&(pstSensorImageMode->u16Height <= 720))
    {
        if (pstSensorImageMode->f32Fps <= 60)
        {
            u8SensorImageMode = SENSOR_720P_30FPS_MODE;
        }
        else
        {
            printf("Not support! Width:%d, Height:%d, Fps:%f\n", 
                pstSensorImageMode->u16Width, 
                pstSensorImageMode->u16Height,
                pstSensorImageMode->f32Fps);

            return -1;
        }
    }
    else if((pstSensorImageMode->u16Width <= 1920)&&(pstSensorImageMode->u16Height <= 1080))
    {
        if (pstSensorImageMode->f32Fps <= 30)
        {
            u8SensorImageMode = SENSOR_1080P_30FPS_MODE;
        }
        else
        {
            printf("Not support! Width:%d, Height:%d, Fps:%f\n", 
                pstSensorImageMode->u16Width, 
                pstSensorImageMode->u16Height,
                pstSensorImageMode->f32Fps);
            
            return -1;
        }
    }
    else
    {
        printf("Not support! Width:%d, Height:%d, Fps:%f\n", 
            pstSensorImageMode->u16Width, 
            pstSensorImageMode->u16Height,
            pstSensorImageMode->f32Fps);
    }

    /* Sensor first init */
    if (HI_FALSE == bSensorInit)
    {
        gu8SensorImageMode = u8SensorImageMode;

        return 0;
    }

    /* Switch SensorImageMode */
    if (u8SensorImageMode == gu8SensorImageMode)
    {
        /* Don't need to switch SensorImageMode */
        return -1;
    }
    
    gu8SensorImageMode = u8SensorImageMode;

    return 0;
    
}


HI_U32 cmos_get_sns_regs_info_imx224(ISP_SNS_REGS_INFO_S *pstSnsRegsInfo)
{
    HI_S32 i;

    if (HI_FALSE == bInit)
    {
        g_stSnsRegsInfo.enSnsType = ISP_SNS_SSP_TYPE;
        g_stSnsRegsInfo.u8Cfg2ValidDelayMax = 2;        
        g_stSnsRegsInfo.u32RegNum = 9;        
        
        for (i=0; i<g_stSnsRegsInfo.u32RegNum; i++)
        {
            g_stSnsRegsInfo.astSspData[i].bUpdate = HI_TRUE;
            g_stSnsRegsInfo.astSspData[i].u32DevAddr = 0x02;
            g_stSnsRegsInfo.astSspData[i].u32DevAddrByteNum = 1;
            g_stSnsRegsInfo.astSspData[i].u32RegAddrByteNum = 1;
            g_stSnsRegsInfo.astSspData[i].u32DataByteNum = 1;
        }        
        g_stSnsRegsInfo.astSspData[0].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[0].u32RegAddr = SHS1_ADDR;//shutter SHS1[7:0]
        g_stSnsRegsInfo.astSspData[1].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[1].u32RegAddr = SHS1_ADDR + 1;//shutter SHS1[15:8]
        g_stSnsRegsInfo.astSspData[2].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[2].u32RegAddr = SHS1_ADDR + 2;//shutter SHS1[16]

        g_stSnsRegsInfo.astSspData[3].u8DelayFrmNum = 0;       //make shutter and gain effective at the same time
        g_stSnsRegsInfo.astSspData[3].u32RegAddr = GAIN_ADDR;  //gain 
        g_stSnsRegsInfo.astSspData[4].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[4].u32RegAddr = GAIN_ADDR + 1;
        g_stSnsRegsInfo.astSspData[5].u8DelayFrmNum = 1;       
        g_stSnsRegsInfo.astSspData[5].u32RegAddr = HCG_ADDR;    

        g_stSnsRegsInfo.astSspData[6].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[6].u32RegAddr = VMAX_ADDR;
        g_stSnsRegsInfo.astSspData[7].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[7].u32RegAddr = VMAX_ADDR + 1;
        g_stSnsRegsInfo.astSspData[8].u8DelayFrmNum = 0;
        g_stSnsRegsInfo.astSspData[8].u32RegAddr = VMAX_ADDR + 2;
      
        bInit = HI_TRUE;
    }
    else
    {
        for (i=0; i<g_stSnsRegsInfo.u32RegNum; i++)
        {
            if (g_stSnsRegsInfo.astSspData[i].u32Data == g_stPreSnsRegsInfo.astSspData[i].u32Data)
            {
                g_stSnsRegsInfo.astSspData[i].bUpdate = HI_FALSE;
            }
            else
            {
                g_stSnsRegsInfo.astSspData[i].bUpdate = HI_TRUE;
            }
        }
    }
    
    if (HI_NULL == pstSnsRegsInfo)
    {
        printf("null pointer when get sns reg info!\n");
        return -1;
    }

    memcpy(pstSnsRegsInfo, &g_stSnsRegsInfo, sizeof(ISP_SNS_REGS_INFO_S)); 
    memcpy(&g_stPreSnsRegsInfo, &g_stSnsRegsInfo, sizeof(ISP_SNS_REGS_INFO_S)); 

    return 0;
}

HI_VOID sensor_global_init_imx224()
{   
    gu8SensorImageMode = SENSOR_720P_30FPS_MODE;
    genSensorMode = WDR_MODE_NONE;
    gu32FullLinesStd = VMAX_720P30; 
    gu32FullLines = VMAX_720P30;
    bInit = HI_FALSE;
    bSensorInit = HI_FALSE; 

    memset(&g_stSnsRegsInfo, 0, sizeof(ISP_SNS_REGS_INFO_S));
    memset(&g_stPreSnsRegsInfo, 0, sizeof(ISP_SNS_REGS_INFO_S));
    
#ifdef INIFILE_CONFIG_MODE 
    HI_S32 s32Ret = HI_SUCCESS;
    s32Ret = Cmos_LoadINIPara(pcName);
    if (HI_SUCCESS != s32Ret)
    {
        printf("Cmos_LoadINIPara failed!!!!!!\n");
    }
#else

#endif    
}
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* End of #ifdef __cplusplus */

#endif 
