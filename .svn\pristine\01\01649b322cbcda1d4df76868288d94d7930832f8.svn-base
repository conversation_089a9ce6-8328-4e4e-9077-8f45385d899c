/*----------------------------------------------------------------------------
 * Copyright (c) <2013-2015>, <Huawei Technologies Co., Ltd>
 * All rights reserved.
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 * 1. Redistributions of source code must retain the above copyright notice, this list of
 * conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list
 * of conditions and the following disclaimer in the documentation and/or other materials
 * provided with the distribution.
 * 3. Neither the name of the copyright holder nor the names of its contributors may be used
 * to endorse or promote products derived from this software without specific prior written
 * permission.
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *---------------------------------------------------------------------------*/
/*----------------------------------------------------------------------------
 * Notice of Export Control Law
 * ===============================================
 * Huawei LiteOS may be subject to applicable export control laws and regulations, which might
 * include those applicable to Huawei LiteOS of U.S. and the country in which you are located.
 * Import, export and usage of Huawei LiteOS in any manner by you shall be in compliance with such
 * applicable export control laws and regulations.
 *---------------------------------------------------------------------------*/
#ifndef __HISOC_MMC_H_
#define __HISOC_MMC_H_

/************************************************************************/

#include "asm/platform.h"
#include "mmc/mmc_os_adapt.h"
#include "mmc/host.h"
#include "himci_reg.h"
#include "himci.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */

/************************************************************************/
#ifdef LOSCFG_DRIVERS_EMMC
#define EMMC_DDR50
#define EMMC_HS200
#endif

#define MMC0    0
#define MMC1    1
/* #define MMC2    2 */
#define MAX_MMC_NUM    2

#define MMC_FREQ_99M       99000000
#define MMC_FREQ_74_25M    74250000
#define MMC_FREQ_49_5M     49500000
#define MMC_FREQ_148_5M    148500000

#define CONFIG_MMC0_CCLK_MIN    60000      //60KHz
#define CONFIG_MMC0_CCLK_MAX    99000000   //99MHz
#define CONFIG_MMC1_CCLK_MIN    60000      //60KHz
#define CONFIG_MMC1_CCLK_MAX    49500000   //49.5MHz

#define CONFIG_MMC0_CLK        99000000
#define CONFIG_MMC1_CLK        49500000

#define CONFIG_MAX_BLK_COUNT    2048
#define CONFIG_MAX_BLK_SIZE     512

#define HIMMC_PAGE_SIZE    4096

/* register mapping*/
#define PERI_CRG49              (CRG_REG_BASE + 0xC4)

#define REG_CTRL_SDIO0_CCLK     (IO_CTL_REG_BASE + 0xb4)
#define REG_CTRL_SDIO0_CCMD     (IO_CTL_REG_BASE + 0xb8)
#define REG_CTRL_SDIO0_CDATA0   (IO_CTL_REG_BASE + 0xbc)
#define REG_CTRL_SDIO0_CDATA1   (IO_CTL_REG_BASE + 0xc0)
#define REG_CTRL_SDIO0_CDATA2   (IO_CTL_REG_BASE + 0xc4)
#define REG_CTRL_SDIO0_CDATA3   (IO_CTL_REG_BASE + 0xc8)

/* sdio0:eMMC pad ctrl reg */
#define REG_CTRL_SDIO0_1_CCLK    (IO_CTL_REG_BASE + 0xcc)
#define REG_CTRL_SDIO0_1_CCMD    (IO_CTL_REG_BASE + 0xd8)
#define REG_CTRL_SDIO0_1_CDATA0  (IO_CTL_REG_BASE + 0xdc)
#define REG_CTRL_SDIO0_1_CDATA1  (IO_CTL_REG_BASE + 0xf8)
#define REG_CTRL_SDIO0_1_CDATA2  (IO_CTL_REG_BASE + 0xfc)
#define REG_CTRL_SDIO0_1_CDATA3  (IO_CTL_REG_BASE + 0xd4)
#define REG_CTRL_SDIO0_1_CDATA4  (IO_CTL_REG_BASE + 0xd0)
#define REG_CTRL_SDIO0_1_CDATA5  (IO_CTL_REG_BASE + 0xec)
#define REG_CTRL_SDIO0_1_CDATA6  (IO_CTL_REG_BASE + 0xe8)
#define REG_CTRL_SDIO0_1_CDATA7  (IO_CTL_REG_BASE + 0xf0)

#define REG_CTRL_SDIO1_CCLK     (IO_CTL_REG_BASE + 0x10)
#define REG_CTRL_SDIO1_CCMD     (IO_CTL_REG_BASE + 0x28)
#define REG_CTRL_SDIO1_CDATA0   (IO_CTL_REG_BASE + 0x20)
#define REG_CTRL_SDIO1_CDATA1   (IO_CTL_REG_BASE + 0x1c)
#define REG_CTRL_SDIO1_CDATA2   (IO_CTL_REG_BASE + 0x34)
#define REG_CTRL_SDIO1_CDATA3   (IO_CTL_REG_BASE + 0x24)

/* clock cfg*/
#define SDIO0_CLK_SEL_MASK      (3U << 10)
#define SDIO0_CLK_SEL_99M       (0U << 10) /* 99MHz */
#define SDIO0_CLK_SEL_74_25M    (1U << 10) /* 74.25MHz */
#define SDIO0_CLK_SEL_49_5M     (2U << 10) /* 49.5MHz */
#define SDIO0_CLK_SEL_148_5M    (3U << 10) /* 148.5MHz */
#define SDIO0_CKEN              (1U << 9)
#define SDIO0_RESET             (1U << 8)

#define SDIO1_CLK_SEL_MASK      (3U << 2)
#define SDIO1_CLK_SEL_49_5M     (0U << 2) /* 49.5MHz */
#define SDIO1_CKEN              (1U << 1)
#define SDIO1_RESET             (1U << 0)

/* pad ctrl */
/* 3.3V */
#define SDIO_CCLK_DS_3V3                0x60
#define SDIO_CCMD_DS_3V3                0xe0
#define SDIO_CDATA0_DS_3V3              0xe0
#define SDIO_CDATA1_DS_3V3              0xe0
#define SDIO_CDATA2_DS_3V3              0xe0
#define SDIO_CDATA3_DS_3V3              0xe0
/* 1.8V */
#define SDIO_CCLK_DS_1V8                0x40
#define SDIO_CCMD_DS_1V8                0xd0
#define SDIO_CDATA0_DS_1V8              0xd0
#define SDIO_CDATA1_DS_1V8              0xd0
#define SDIO_CDATA2_DS_1V8              0xd0
#define SDIO_CDATA3_DS_1V8              0xd0

/* MISC ctrl polarity */
#define SDIO_MISC_POLAR                 0x04
#define SDIO_CLK_99M                   99000000
#define SDIO_CLK_74_25M                74250000
#define SDIO_CLK_49_5M                 49500000
#define SDIO_CLK_148_5M                148500000

#define SDIO1_CLK_OFFSET    (2)
#define SDIO0_CLK_OFFSET    (10)

#define PHASE_SHIFT        0x2030000
#define READ_THRESHOLD_SIZE    0x2000001
#define DRV_PHASE_SHIFT             (0x2)
#define SMPL_PHASE_SHIFT            (0x2)

/*FIXME: CFG_PHASE_IN_TIMING
 *  *  * if open, it means that we have to config
 *   * phase when setting timing.
 *    */
//#define CFG_PHASE_IN_TIMING
#define HIMCI_PHASE_SCALE 8
#define TUNING_START_PHASE  0
#define TUNING_END_PHASE    7

#define PHASE_NOT_FOUND -1


#define hi_mci_detect_polarity_cfg(mmc_num)    do{\
    unsigned int reg_value = 0;\
    reg_value = himci_readl(MISC_REG_BASE+SDIO_MISC_POLAR);\
    reg_value &= (~(1U << mmc_num));\
    himci_writel(reg_value, MISC_REG_BASE+SDIO_MISC_POLAR);\
}while(0)


#define hi_mci_soft_reset(mmc_num) do { \
    unsigned int reg_value = 0; \
    reg_value = himci_readl(PERI_CRG49); \
    if (MMC0 == mmc_num) \
        reg_value |= SDIO0_RESET; \
    else if (MMC1 == mmc_num) \
        reg_value |= SDIO1_RESET; \
    himci_writel(reg_value, PERI_CRG49); \
    mmc_delay_us(100); \
    if (MMC0 == mmc_num) \
        reg_value &= (~SDIO0_RESET); \
    else if (MMC1 == mmc_num) \
        reg_value &= (~SDIO1_RESET); \
    himci_writel(reg_value, PERI_CRG49); \
}while(0)

/* *
 * MMC HOST useable
 * Set to 1: useable
 * Set to 0: not useable
 * */
#define USE_MMC0    1
#define USE_MMC1    1

#define USE_THIS_MMC(mmc_num) ({ \
    int value = 0; \
    if (MMC0 == mmc_num) \
        value = USE_MMC0; \
    else if (MMC1 == mmc_num) \
        value = USE_MMC1; \
    value; \
})
//#define CONFIG_SEND_AUTO_STOP /* open auto stop */

/* MCI_FIFOTH(0x4c) details */
#define BURST_SIZE        (0x6<<28)
#define RX_WMARK        (0x7f<<16)
#define TX_WMARK        0x80

#define MCI_BMOD_VALUE  BURST_INCR
#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */
#endif

