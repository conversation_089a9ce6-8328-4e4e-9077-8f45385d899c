/*
 * Copyright (C) 2008 The Android Open Source Project
 * Copyright (c) <2013-2018>, <Huawei Technologies Co., Ltd>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *  * Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *  * Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON>ILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIA<PERSON> DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */

/*----------------------------------------------------------------------------
 * Notice of Export Control Law
 * ===============================================
 * Huawei LiteOS may be subject to applicable export control laws and regulations, which might
 * include those applicable to Huawei LiteOS of U.S. and the country in which you are located.
 * Import, export and usage of Huawei LiteOS in any manner by you shall be in compliance with such
 * applicable export control laws and regulations.
 *---------------------------------------------------------------------------*/

#ifndef _SYS_TIME_H_
#define _SYS_TIME_H_

#include <sys/cdefs.h>
#include <sys/types.h>
#ifndef __LITEOS__
#include <linux/time.h>
#else
#include <liteos/time.h>
#endif

/* POSIX says <sys/time.h> gets you most of <sys/select.h> and may get you all of it. */
#include <sys/select.h>

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif /* __cplusplus */
#endif /* __cplusplus */


#if defined(__LITEOS__) && !defined(__LP64___)
/**
* @ingroup  time
* @par Description:
* This function  sets the time as well as a timezone in 32-bit system.
*
* @attention
* <ul>
* <li>The function is not supported to set timezone,So the second parameter is unused</li>
* </ul>
*
* @retval #0  The function is executed successfully.
* @retval #-1 The function failed to execute, and corresponding error code is set.
*
* @par Errors
* <ul>
* <li><b>EINVAL</b>: An invalid Input.</li>
* </ul>
*
* @par Dependency:
* <ul><li>time.h</li></ul>
*
* @see  clock_gettime | time | ctime
*
* @since Huawei LiteOS V100R001C00
*/
int settimeofday64(const struct timeval64*, const struct timezone*);

/**
* @ingroup  time
* @par Description:
* This function  gets the time as well as a timezone in 32-bit system.
*
* @attention
* <ul>
* <li>The function is not supported to get timezone,So the second parameter is unused</li>
* </ul>
*
* @retval #0  The function is executed successfully.
* @retval #-1 The function failed to execute, and corresponding error code is set.
*
* @par Errors
* <ul>
* <li><b>EINVAL</b>: An invalid Input.</li>
* </ul>
*
* @par Dependency:
* <ul><li>time.h</li></ul>
*
* @see  clock_gettime | time | ctime
*
* @since Huawei LiteOS V100R001C00
*/
int gettimeofday64(struct timeval64*, struct timezone*);
#endif

/**
* @ingroup  time
* @par Description:
* This function  gets the time as well as a timezone.
*
* @attention
* <ul>
* <li>The function is not supported to get timezone,So the second parameter is unused</li>
* </ul>
*
* @retval #0  The function is executed successfully.
* @retval #-1 The function failed to execute, and corresponding error code is set.
*
* @par Errors
* <ul>
* <li><b>EINVAL</b>: An invalid Input.</li>
* </ul>
*
* @par Dependency:
* <ul><li>time.h</li></ul>
*
* @see  clock_gettime | time | ctime
*
* @since Huawei LiteOS V100R001C00
*/
extern int gettimeofday(struct timeval *, struct timezone *);

/**
* @ingroup  time
* @par Description:
* This function  sets the time as well as a timezone.
*
* @attention
* <ul>
* <li>The function is not supported to set timezone,So the second parameter is unused</li>
* </ul>
*
* @retval #0  The function is executed successfully.
* @retval #-1 The function failed to execute, and corresponding error code is set.
*
* @par Errors
* <ul>
* <li><b>EINVAL</b>: An invalid Input.</li>
* </ul>
*
* @par Dependency:
* <ul><li>time.h</li></ul>
*
* @see  clock_gettime | time | ctime
*
* @since Huawei LiteOS V100R001C00
*/
extern int settimeofday(const struct timeval *, const struct timezone *);

extern int getitimer(int, struct itimerval *);
extern int setitimer(int, const struct itimerval *, struct itimerval *);

/**
* @ingroup  time
* @par Description:
* This function  gradually adjusts the system clock (as returned by gettimeofday).
*
* @attention
* <ul>
* <li>None.</li>
* </ul>
*
* @retval #0  The function is executed successfully.
* @retval #-1 The function failed to execute, and corresponding error code is set.
*
* @par Errors
* <ul>
* <li><b>EINVAL</b>: An invalid input.</li>
* </ul>
*
* @par Dependency:
* <ul><li>time.h</li></ul>
*
* @see  gettimeofday | time
*
* @since Huawei LiteOS V100R001C00
*/
extern int adjtime(const struct timeval *delta, struct timeval *olddelta);

extern int utimes(const char *, const struct timeval *);

#define timerclear(a)   \
        ((a)->tv_sec = (a)->tv_usec = 0)

#define timerisset(a)    \
        ((a)->tv_sec != 0 || (a)->tv_usec != 0)

#define timercmp(a, b, op)               \
        ((a)->tv_sec == (b)->tv_sec      \
        ? (a)->tv_usec op (b)->tv_usec   \
        : (a)->tv_sec op (b)->tv_sec)

#define timeradd(a, b, res)                           \
    do {                                              \
        (res)->tv_sec  = (a)->tv_sec  + (b)->tv_sec;  \
        (res)->tv_usec = (a)->tv_usec + (b)->tv_usec; \
        if ((res)->tv_usec >= 1000000) {              \
            (res)->tv_usec -= 1000000;                \
            (res)->tv_sec  += 1;                      \
        }                                             \
    } while (0)

#define timersub(a, b, res)                           \
    do {                                              \
        (res)->tv_sec  = (a)->tv_sec  - (b)->tv_sec;  \
        (res)->tv_usec = (a)->tv_usec - (b)->tv_usec; \
        if ((res)->tv_usec < 0) {                     \
            (res)->tv_usec += 1000000;                \
            (res)->tv_sec  -= 1;                      \
        }                                             \
    } while (0)

#define TIMEVAL_TO_TIMESPEC(tv, ts) {     \
    (ts)->tv_sec = (tv)->tv_sec;          \
    (ts)->tv_nsec = (tv)->tv_usec * 1000; \
}
#define TIMESPEC_TO_TIMEVAL(tv, ts) {     \
    (tv)->tv_sec = (ts)->tv_sec;          \
    (tv)->tv_usec = (ts)->tv_nsec / 1000; \
}


#ifdef __cplusplus
#if __cplusplus
}
#endif /* __cplusplus */
#endif /* __cplusplus */

#endif /* _SYS_TIME_H_ */
